# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Google Play Store Review Extractor - a Flask web application that scrapes and analyzes reviews from Google Play Store applications. The app provides filtering, visualization, and export capabilities with a responsive dark/light mode interface.

## Development Commands

### Environment Setup
```bash
# Create and activate conda environment
conda create --name playstore_reviews_extraction python=3.9
conda activate playstore_reviews_extraction

# Install dependencies
pip install -r requirements.txt
```

### Running the Application
```bash
# Start the Flask development server
python app.py
```
The server automatically tries ports 5000-5010 until it finds an available one.

### Testing
```bash
# Test basic API endpoint
curl http://localhost:5000/test
```

## Architecture

### Core Components

**app.py** - Main Flask application with the following key patterns:
- Stream-based review extraction using Server-Sent Events (SSE) for real-time progress
- Custom exception handling for different error types (AppNotFoundError, NetworkError, etc.)
- Batch processing of reviews (100 per batch) with continuation tokens
- In-memory Excel generation using xlsxwriter

**Frontend Structure:**
- Single-page application using Bootstrap 5 and vanilla JavaScript
- Real-time progress tracking via EventSource API
- Chart.js for data visualization
- Flatpickr for date selection
- Theme switching with CSS custom properties

### Data Flow
1. Frontend sends review extraction request to `/api/reviews`
2. Backend streams data in JSON chunks: app details → reviews batches → progress → completion
3. Frontend accumulates reviews and updates UI in real-time
4. Export functionality sends accumulated data to `/api/export` for Excel generation

### Key Dependencies
- **google-play-scraper**: Core scraping functionality
- **xlsxwriter**: Excel export (in-memory generation)
- **Flask**: Web framework with streaming support
- **pandas/numpy**: Data processing (installed but minimal usage in current code)

### Error Handling Strategy
The application uses custom exceptions for different failure modes:
- AppNotFoundError: Invalid app ID
- NetworkError: Connection/API issues  
- DateRangeError: Invalid date inputs
- ScrapingError: Review extraction failures

### Filtering System
Reviews are filtered server-side by:
- Date range (start_date/end_date)
- Rating range (min_rating/max_rating) 
- Keywords (comma-separated, case-insensitive)

## File Structure
```
playstore-reviews-extraction/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── static/
│   ├── css/style.css     # Responsive styles with CSS variables
│   └── js/theme.js       # Dark/light mode toggle
└── templates/
    └── index.html        # Single-page application template
```