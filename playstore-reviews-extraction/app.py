import sys
import os
import site
import subprocess
from importlib.metadata import version
from flask import Flask, request, jsonify, render_template, send_file, Response, url_for
from google_play_scraper import app as gplay_app, Sort, reviews
import pandas as pd
import numpy as np
import traceback
from datetime import datetime
import io
import xlsxwriter
import json
import re

# Add these at the top of your app.py file, after the imports


class AppNotFoundError(Exception):
    pass


class NetworkError(Exception):
    pass


class DateRangeError(Exception):
    pass


class ScrapingError(Exception):
    pass


print(f"Python version: {sys.version}")
print(f"Python path: {sys.executable}")
print(f"Working directory: {os.getcwd()}")
print(f"sys.path: {sys.path}")
print("Site packages:")
for package in site.getsitepackages():
    print(package)

print("\nInstalled packages:")
result = subprocess.run(
    [sys.executable, "-m", "pip", "list"], capture_output=True, text=True
)
print(result.stdout)

# Try to find the location of the flask module
try:
    import flask

    print(f"Flask version: {version('flask')}")
    print(f"Flask location: {flask.__file__}")
except ImportError as e:
    print(f"Error importing Flask: {e}")

# Try to find the location of the google_play_scraper module
try:
    import google_play_scraper

    print(f"google_play_scraper location: {google_play_scraper.__file__}")
    # Attempt to get the version, but don't fail if it's not available
    try:
        print(f"google_play_scraper version: {version('google-play-scraper')}")
    except Exception:
        print("google_play_scraper version: Not available")
except ImportError as e:
    print(f"Error importing google_play_scraper: {e}")

# Now let's try to import the specific functions we need
try:
    from google_play_scraper import app, Sort

    print("Successfully imported app and Sort from google_play_scraper")
except ImportError as e:
    print(f"Error importing app and Sort from google_play_scraper: {e}")

# Uncomment and test other imports
import pandas as pd
import numpy as np

print("All required modules imported successfully")

try:
    import xlsxwriter

    print(f"XlsxWriter version: {xlsxwriter.__version__}")
    print(f"XlsxWriter location: {xlsxwriter.__file__}")
except ImportError as e:
    print(f"Error importing XlsxWriter: {e}")

app = Flask(__name__, static_url_path="/static")


@app.route("/")
def index():
    return render_template("index.html")


@app.route("/api/reviews", methods=["POST"])
def get_reviews():
    app_id = request.json.get("app_id")
    start_date = request.json.get("start_date")
    end_date = request.json.get("end_date")
    min_rating = request.json.get("min_rating")
    max_rating = request.json.get("max_rating")
    keywords = request.json.get("keywords")

    if not app_id:
        return jsonify({"success": False, "error": "No app_id provided"}), 400

    def generate():
        try:
            print(f"Attempting to fetch reviews for app ID: {app_id}")

            try:
                # Fetch app details
                app_details = gplay_app(app_id, lang="en", country="us")
            except ValueError:
                raise AppNotFoundError(f"App with ID '{app_id}' not found")
            except Exception as e:
                raise NetworkError(f"Failed to fetch app details: {str(e)}")

            app_name = app_details["title"]
            print(f"App name: {app_name}")
            yield json.dumps({"type": "app_details", "data": app_name}) + "\n"

            # Validate date range
            if start_date and end_date:
                try:
                    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
                    if start_date_obj > end_date_obj:
                        raise DateRangeError("Start date cannot be after end date")
                except ValueError:
                    raise DateRangeError("Invalid date format. Use YYYY-MM-DD")

            # Fetch all reviews
            continuation_token = None
            total_fetched = 0
            date_limit_reached = False

            while not date_limit_reached:
                print(f"Fetching batch. Total fetched so far: {total_fetched}")
                try:
                    batch, continuation_token = reviews(
                        app_id,
                        lang="en",
                        country="us",
                        sort=Sort.NEWEST,
                        count=100,  # Fetch in batches of 100
                        continuation_token=continuation_token,
                    )
                except Exception as e:
                    raise ScrapingError(f"Failed to fetch reviews: {str(e)}")

                if not batch:
                    print("No more reviews to fetch")
                    break

                # Filter reviews
                filtered_batch = []
                for review in batch:
                    if start_date and end_date:
                        review_date = review["at"].replace(tzinfo=None)
                        if not (start_date_obj <= review_date <= end_date_obj):
                            continue

                    if min_rating and review["score"] < int(min_rating):
                        continue

                    if max_rating and review["score"] > int(max_rating):
                        continue

                    if keywords:
                        keyword_list = [k.strip().lower() for k in keywords.split(",")]
                        review_text = review["content"].lower()
                        if not any(keyword in review_text for keyword in keyword_list):
                            continue

                    filtered_batch.append(review)

                if len(filtered_batch) < len(batch):
                    date_limit_reached = True

                total_fetched += len(filtered_batch)

                # Convert reviews to a list of dictionaries
                reviews_list = [
                    {
                        "userName": review["userName"],
                        "score": review["score"],
                        "content": review["content"],
                        "at": review["at"].isoformat(),
                    }
                    for review in filtered_batch
                ]

                if reviews_list:
                    print(f"Sending batch of {len(reviews_list)} reviews")
                    yield json.dumps({"type": "reviews", "data": reviews_list}) + "\n"
                yield json.dumps({"type": "progress", "data": total_fetched}) + "\n"

                if not continuation_token:
                    print("No more reviews to fetch")
                    break

            print(f"Fetched {total_fetched} reviews in total")
            yield json.dumps({"type": "complete", "data": total_fetched}) + "\n"

        except AppNotFoundError as e:
            print(f"App not found: {str(e)}")
            yield json.dumps({"type": "error", "data": str(e)}) + "\n"
        except NetworkError as e:
            print(f"Network error: {str(e)}")
            yield json.dumps({"type": "error", "data": str(e)}) + "\n"
        except DateRangeError as e:
            print(f"Date range error: {str(e)}")
            yield json.dumps({"type": "error", "data": str(e)}) + "\n"
        except ScrapingError as e:
            print(f"Scraping error: {str(e)}")
            yield json.dumps({"type": "error", "data": str(e)}) + "\n"
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            print(traceback.format_exc())
            yield (
                json.dumps(
                    {"type": "error", "data": f"An unexpected error occurred: {str(e)}"}
                )
                + "\n"
            )

    return Response(generate(), mimetype="text/event-stream")


@app.route("/test", methods=["GET"])
def test():
    return jsonify({"message": "Test successful"}), 200


@app.route("/api/export", methods=["POST"])
def export_reviews():
    try:
        data = request.json
        app_name = data.get("app_name")
        reviews = data.get("reviews")

        # Create an in-memory output file
        output = io.BytesIO()

        # Create a new workbook and add a worksheet
        workbook = xlsxwriter.Workbook(output, {"in_memory": True})
        worksheet = workbook.add_worksheet()

        # Add headers
        headers = ["User Name", "Rating", "Review", "Date"]
        for col, header in enumerate(headers):
            worksheet.write(0, col, header)

        # Add data
        for row, review in enumerate(reviews, start=1):
            worksheet.write(row, 0, review["userName"])
            worksheet.write(row, 1, review["score"])
            worksheet.write(row, 2, review["content"])
            worksheet.write(row, 3, review["at"])

        workbook.close()

        # Seek to the beginning of the stream
        output.seek(0)

        return send_file(
            output,
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            as_attachment=True,
            download_name=f"{app_name}_reviews.xlsx",
        )

    except Exception as e:
        print(f"Error exporting reviews: {str(e)}")
        print(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)}), 500


if __name__ == "__main__":
    # Try ports 5000 to 5010 until we find an open one
    for port in range(5000, 5011):
        try:
            app.run(debug=True, port=port)
            print(f"Server is running on http://localhost:{port}")
            break
        except OSError:
            continue
    else:
        print("Unable to find an open port. Please free up a port and try again.")
