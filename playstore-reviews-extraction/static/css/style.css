/* Base styles */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    color: white;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(30,60,114,0.9) 0%, rgba(42,82,152,0.8) 50%, rgba(102,126,234,0.7) 100%);
}

.hero-section .container {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 1200px;
}

.hero-branding {
    font-size: 0.9rem;
    font-weight: 500;
    opacity: 0.9;
    margin-bottom: 1rem;
    letter-spacing: 0.5px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.hero-title .highlight {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24, #ff3742);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.1rem;
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 2.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-hero-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    color: white;
    padding: 12px 30px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.btn-hero-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    color: white;
}

.btn-hero-secondary {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 28px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-hero-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    color: white;
}

/* Main Content */
.main-content {
    background-color: var(--bg-color);
    min-height: 100vh;
    padding: 0.5rem 0;
    position: relative;
}

/* Parameters Card */
.parameters-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
}

.parameters-card .card-header {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-icon {
    color: #ff6b6b;
    font-size: 1.2rem;
}

.parameters-card h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.parameters-card .card-body {
    padding: 2rem;
}

.form-label {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    opacity: 0.8;
}

.btn-extract {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    color: white;
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    margin-top: 1rem;
}

.btn-extract:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
    color: white;
}

.btn-extract span {
    margin-right: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .btn-hero-primary,
    .btn-hero-secondary {
        width: 200px;
    }
    
    .parameters-card .card-body {
        padding: 1.5rem;
    }
}

/* Theme colors */
:root {
    --bg-color: #f8f9fa;
    --text-color: #333;
    --card-bg: #fff;
    --input-bg: #fff;
    --input-text: #333;
    --input-border: #ced4da;
    --input-placeholder: #6c757d;
    --heading-color: #000;
    --chart-text: #333;
    --review-name: #333;
}

[data-theme="dark"] {
    --bg-color: #0a0a0f;  /* Very dark background */
    --text-color: #fff;   /* White text for better contrast */
    --card-bg: #1a1a2e;   /* Darker card background */
    --input-bg: #16213e;
    --input-text: #fff;   /* White text for inputs */
    --input-border: #2a3a5e;
    --input-placeholder: #aaa;
    --heading-color: #fff;  /* Changed to white for dark mode */
    --chart-text: #fff;   /* White text for charts */
    --review-name: #fff;  /* White text for reviewer names */
}

/* Dark theme hero section */
[data-theme="dark"] .hero-section {
    background: linear-gradient(135deg, #0f1419 0%, #1a1f35 50%, #16213e 100%);
}

[data-theme="dark"] .hero-section::before {
    background: linear-gradient(135deg, rgba(15,20,25,0.9) 0%, rgba(26,31,53,0.8) 50%, rgba(22,33,62,0.7) 100%);
}

/* Dark theme parameters card */
[data-theme="dark"] .parameters-card {
    background: rgba(22, 33, 62, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .parameters-card .card-header {
    background: rgba(255, 255, 255, 0.02);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .main-content {
    background: linear-gradient(180deg, var(--bg-color) 0%, #16213e 100%);
}

/* Component styles */
.review {
    background-color: var(--card-bg);
    color: var(--text-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid var(--input-border);
}

.chart-container {
    display: none;
    background-color: var(--card-bg);
    border: 1px solid var(--input-border);
    width: 100%;
    height: 300px;
    margin-bottom: 20px;
}

/* Chart styles */
[data-theme="dark"] .chart-container {
    background-color: #1e1e1e;  /* Darker background for charts */
    color: var(--chart-text);
}

[data-theme="dark"] .chart-container canvas {
    background-color: #1e1e1e;
}

/* Alert styles */
[data-theme="dark"] .alert-info {
    background-color: #1e1e1e;
    color: #fff;
    border-color: #444;
}

/* Review styles */
[data-theme="dark"] .review {
    background-color: #1e1e1e;
    border-color: #444;
}

/* Form elements */
.form-control, .form-select {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: var(--input-border);
}

.form-control::placeholder {
    color: var(--input-placeholder);
}

.form-control:focus, .form-select:focus {
    background-color: var(--input-bg);
    color: var(--input-text);
    border-color: #666;
    box-shadow: 0 0 0 0.25rem rgba(130, 138, 145, 0.25);
}

/* Theme switch styles */
.theme-switch-wrapper {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 999;
}

.theme-switch {
    display: inline-block;
    height: 34px;
    position: relative;
    width: 60px;
}

.theme-switch input {
    display: none;
}

.slider {
    background-color: #ccc;
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s;
}

.slider:before {
    background-color: #fff;
    bottom: 4px;
    content: "";
    height: 26px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 26px;
}

input:checked + .slider {
    background-color: #66bb6a;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    color: var(--heading-color);
}

/* Form control styles */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: #2c2c2c;
    color: #fff;
    border-color: #444;
}

[data-theme="dark"] .form-control::placeholder {
    color: #aaa;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: #2c2c2c;
    color: #fff;
    border-color: #666;
}

/* Keep your existing styles below, but add these button styles */
[data-theme="dark"] .btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #fff;
}

[data-theme="dark"] .btn-success {
    background-color: #198754;
    border-color: #198754;
    color: #fff;
}

/* Progress bar styles */
[data-theme="dark"] .progress {
    background-color: #2c2c2c;
}

/* Make reviewer name white in dark mode */
.review h5 {
    color: var(--review-name);
    margin-bottom: 10px;
}

/* Make the browser background dark in dark mode */
[data-theme="dark"] {
    background-color: #121212;
}

[data-theme="dark"] body {
    background-color: #121212;
}
