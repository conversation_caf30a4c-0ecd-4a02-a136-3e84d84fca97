<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apple App Store Review Extractor</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- Theme switch wrapper -->
    <div class="theme-switch-wrapper">
        <label class="theme-switch">
            <input type="checkbox" id="checkbox">
            <div class="slider round"></div>
        </label>
    </div>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-12">
                    <div class="hero-branding">App Store Analytics</div>
                    <h1 class="hero-title">Apple App Store <span class="highlight">Review Extractor</span></h1>
                    <p class="hero-subtitle">Extract and analyze user reviews from the Apple App Store<br>with advanced filtering, real-time visualization, and<br>comprehensive data export capabilities.</p>
                    <div class="hero-buttons">
                        <button onclick="scrollToForm()" class="btn btn-hero-primary">Extract Reviews</button>
                        <button onclick="showLearnMore()" class="btn btn-hero-secondary">Learn More</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="container">
            <!-- Parameters Form Card -->
            <div class="parameters-card">
                <div class="card-header">
                    <div class="card-icon">★</div>
                    <h3>Review Extraction Parameters</h3>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-12">
                            <label class="form-label">APP ID:</label>
                            <input type="text" id="app-id" class="form-control" placeholder="e.g., 123456789">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">START DATE:</label>
                            <input type="text" id="date-from" class="form-control" placeholder="05/22/2025">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">END DATE:</label>
                            <input type="text" id="date-to" class="form-control" placeholder="06/23/2025">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">MIN RATING:</label>
                            <select id="min-rating" class="form-select">
                                <option value="">Any</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">MAX RATING:</label>
                            <select id="max-rating" class="form-select">
                                <option value="">Any</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <label class="form-label">KEYWORDS (OPTIONAL, COMMA-SEPARATED):</label>
                            <input type="text" id="keywords" class="form-control" placeholder="e.g., bug, feature, update">
                        </div>
                        <div class="col-12">
                            <label class="form-label">COUNTRY:</label>
                            <select id="country" class="form-select">
                                <option value="">All Countries</option>
                                <option value="us">United States</option>
                                <option value="gb">United Kingdom</option>
                                <option value="ca">Canada</option>
                                <option value="au">Australia</option>
                                <option value="de">Germany</option>
                                <option value="fr">France</option>
                                <option value="jp">Japan</option>
                                <option value="kr">South Korea</option>
                                <option value="cn">China</option>
                                <option value="in">India</option>
                            </select>
                        </div>
                        <div class="col-12">
                            <button onclick="getReviews()" class="btn btn-extract w-100">
                                <span>▼</span> Extract Reviews
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        <div id="progress-container" class="mb-4" style="display: none;">
            <div class="progress">
                <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
        </div>
        <!-- Move the review-count div here, above the export-container -->
        <div id="review-count" class="alert alert-info mb-4" style="display: none;"></div>
        <div id="export-container" class="mb-4" style="display: none;">
            <button onclick="exportToExcel()" class="btn btn-success">Export to Excel</button>
        </div>
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="chart-container">
                    <canvas id="ratingChart"></canvas>
                </div>
            </div>
            <div class="col-md-6">
                <div class="chart-container">
                    <canvas id="timelineChart"></canvas>
                </div>
            </div>
        </div>
        <div id="reviews"></div>
    </div>

    <script>
        let currentReviews = [];
        let currentAppName = null;
        let currentPage = 1;
        let totalPages = 1;

        function scrollToForm() {
            document.querySelector('.parameters-card').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }

        function showLearnMore() {
            alert('Apple App Store Review Extractor\n\nKey Features:\n• Extract reviews with advanced filtering\n• Real-time progress tracking\n• Data visualization with charts\n• Export to Excel format\n• Dark/Light theme support\n\nSimply enter an App ID and customize your extraction parameters to get started!');
        }

        flatpickr("#date-from", {
            dateFormat: "Y-m-d",
            allowInput: true
        });

        flatpickr("#date-to", {
            dateFormat: "Y-m-d",
            allowInput: true
        });

        async function getReviews() {
            const appId = document.getElementById('app-id').value;
            const dateFrom = document.getElementById('date-from').value;
            const dateTo = document.getElementById('date-to').value;
            const minRating = document.getElementById('min-rating').value;
            const maxRating = document.getElementById('max-rating').value;
            const keywords = document.getElementById('keywords').value;
            const reviewsDiv = document.getElementById('reviews');
            const exportContainer = document.getElementById('export-container');
            const progressContainer = document.getElementById('progress-container');
            const progressBar = document.getElementById('progress-bar');
            
            reviewsDiv.innerHTML = '';
            exportContainer.style.display = 'none';
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';
            progressBar.textContent = '0%';
            currentReviews = [];

            try {
                // Show charts when starting to get reviews
                document.querySelectorAll('.chart-container').forEach(container => {
                    container.style.display = 'block';
                });
                
                const response = await fetch(`${window.location.origin}/api/reviews`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        app_id: appId,
                        start_date: dateFrom || null,
                        end_date: dateTo || null,
                        min_rating: minRating || null,
                        max_rating: maxRating || null,
                        keywords: keywords || null
                    }),
                });

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;
                    
                    buffer += decoder.decode(value, { stream: true });
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // Keep the last incomplete line in the buffer

                    for (const line of lines) {
                        if (line.trim() === '') continue;
                        
                        try {
                            const data = JSON.parse(line);
                            handleStreamData(data, reviewsDiv, progressBar);
                        } catch (parseError) {
                            console.error("Error parsing JSON:", parseError, "Line:", line);
                        }
                    }
                }

                exportContainer.style.display = 'block';
                // Add this at the end of the try block in getReviews function
                console.log("Finished receiving all data. Updating charts one last time.");
                updateCharts();
            } catch (error) {
                console.error("Error:", error);
                reviewsDiv.innerHTML = `<div class="alert alert-danger" role="alert">Error: ${error.message || 'Unknown error occurred'}</div>`;
            } finally {
                progressContainer.style.display = 'none';
            }
        }

        function handleStreamData(data, reviewsDiv, progressBar) {
            switch (data.type) {
                case 'app_details':
                    currentAppName = data.data;
                    reviewsDiv.innerHTML = `<h2 class="mb-3">Reviews for ${currentAppName}</h2>`;
                    break;
                case 'reviews':
                    currentReviews.push(...data.data);
                    data.data.forEach(review => {
                        const reviewElement = document.createElement('div');
                        reviewElement.className = 'review';
                        reviewElement.innerHTML = `
                            <h5>${review.userName}</h5>
                            <p><strong>Rating:</strong> ${'★'.repeat(review.score)}${'☆'.repeat(5-review.score)}</p>
                            <p>${review.content}</p>
                            <small class="text-muted">${new Date(review.at).toLocaleString()}</small>
                        `;
                        reviewsDiv.appendChild(reviewElement);
                    });
                    updateReviewCount(currentReviews.length);
                    console.log("Received new reviews. Total reviews:", currentReviews.length);
                    updateCharts();
                    break;
                case 'progress':
                    const progress = Math.min(100, Math.round((data.data / 1000) * 100));
                    progressBar.style.width = `${progress}%`;
                    progressBar.textContent = `${progress}% (${data.data} reviews)`;
                    break;
                case 'complete':
                    console.log(`Fetching complete. Total reviews: ${data.data}`);
                    updateReviewCount(data.data, true);
                    break;
                case 'error':
                    throw new Error(data.data);
            }
        }

        function updateReviewCount(count, isComplete = false) {
            const reviewCountDiv = document.getElementById('review-count');
            reviewCountDiv.style.display = 'block';
            reviewCountDiv.textContent = isComplete 
                ? `Total reviews fetched: ${count}`
                : `Reviews fetched so far: ${count}`;
        }

        function updatePagination() {
            const paginationDiv = document.getElementById('pagination');
            paginationDiv.innerHTML = '';

            if (totalPages > 1) {
                const prevButton = document.createElement('button');
                prevButton.textContent = 'Previous';
                prevButton.className = 'btn btn-secondary';
                prevButton.disabled = currentPage === 1;
                prevButton.onclick = () => getReviews(currentPage - 1);
                paginationDiv.appendChild(prevButton);

                const pageInfo = document.createElement('span');
                pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
                pageInfo.className = 'mx-3';
                paginationDiv.appendChild(pageInfo);

                const nextButton = document.createElement('button');
                nextButton.textContent = 'Next';
                nextButton.className = 'btn btn-secondary';
                nextButton.disabled = currentPage === totalPages;
                nextButton.onclick = () => getReviews(currentPage + 1);
                paginationDiv.appendChild(nextButton);
            }
        }

        async function exportToExcel() {
            if (!currentReviews || !currentAppName) {
                alert('No reviews to export. Please fetch reviews first.');
                return;
            }

            try {
                const response = await axios.post(`${window.location.origin}/api/export`, {
                    app_name: currentAppName,
                    reviews: currentReviews
                }, {
                    responseType: 'blob'
                });

                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', `${currentAppName}_reviews.xlsx`);
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                console.error("Error exporting to Excel:", error);
                alert('Error exporting to Excel. Please try again.');
            }
        }

        function updateCharts() {
            console.log("Updating charts. Current reviews:", currentReviews.length);
            if (currentReviews.length > 0) {
                updateRatingChart();
                updateTimelineChart();
            } else {
                console.log("No reviews to display in charts.");
                // Clear existing charts
                if (window.ratingChart instanceof Chart) {
                    window.ratingChart.destroy();
                }
                if (window.timelineChart instanceof Chart) {
                    window.timelineChart.destroy();
                }
            }
        }

        function updateRatingChart() {
            const ratings = [0, 0, 0, 0, 0];
            currentReviews.forEach(review => ratings[review.score - 1]++);
            console.log("Rating data:", ratings);

            const ctx = document.getElementById('ratingChart').getContext('2d');
            
            // Check if the chart instance exists and is a Chart instance before destroying
            if (window.ratingChart instanceof Chart) {
                window.ratingChart.destroy();
            }
            
            window.ratingChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['1 Star', '2 Stars', '3 Stars', '4 Stars', '5 Stars'],
                    datasets: [{
                        label: 'Number of Reviews',
                        data: ratings,
                        backgroundColor: 'rgba(75, 192, 192, 0.6)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Reviews'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Rating'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Distribution of Ratings'
                        }
                    }
                }
            });
        }

        function updateTimelineChart() {
            const reviewDates = currentReviews.map(review => new Date(review.at).toISOString().split('T')[0]);
            const dateCount = {};
            reviewDates.forEach(date => {
                dateCount[date] = (dateCount[date] || 0) + 1;
            });
            console.log("Timeline data:", dateCount);
            const sortedDates = Object.keys(dateCount).sort();
            const reviewCounts = sortedDates.map(date => dateCount[date]);

            const ctx = document.getElementById('timelineChart').getContext('2d');
            
            // Check if the chart instance exists and is a Chart instance before destroying
            if (window.timelineChart instanceof Chart) {
                window.timelineChart.destroy();
            }
            
            window.timelineChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: sortedDates,
                    datasets: [{
                        label: 'Number of Reviews',
                        data: reviewCounts,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderWidth: 1,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Reviews'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Date'
                            }
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: 'Timeline of Review Submissions'
                        }
                    }
                }
            });
        }

        // Add this function to hide charts initially
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.chart-container').forEach(container => {
                container.style.display = 'none';
            });
        });
    </script>
    <!-- Add this before closing </body> tag -->
    <script src="{{ url_for('static', filename='js/theme.js') }}"></script>
</body>
</html>
